// 页面加载完成后初始化游戏管理器
document.addEventListener('DOMContentLoaded', () => {
    gameManager = new GameManager();
    gameManager.init();
    
    // 添加游戏按钮悬停效果和说明
    const gameButtons = document.querySelectorAll('.game-btn');
    const gameInfo = document.getElementById('game-info');
    
    const gameDescriptions = {
        snake: {
            title: '贪吃蛇',
            description: '经典的贪吃蛇游戏！控制蛇吃食物，避免撞到墙壁或自己的身体。每吃一个食物，蛇会变长，分数增加。'
        },
        breakout: {
            title: '打砖块',
            description: '经典的打砖块游戏！控制挡板反弹球，击破屏幕上的所有砖块。不要让球掉落到底部！'
        },
        shooter: {
            title: '太空射击',
            description: '激动人心的太空射击游戏！控制飞船在太空中战斗，射击敌人，避免被击中。考验你的反应速度！'
        }
    };
    
    gameButtons.forEach(button => {
        button.addEventListener('mouseenter', () => {
            const gameType = button.dataset.game;
            const info = gameDescriptions[gameType];
            gameInfo.innerHTML = `
                <h4>${info.title}</h4>
                <p>${info.description}</p>
            `;
        });
        
        button.addEventListener('mouseleave', () => {
            gameInfo.innerHTML = '<p>选择一个游戏开始游玩！</p>';
        });
    });
    
    // 添加键盘快捷键提示
    console.log('游戏快捷键:');
    console.log('ESC - 返回主菜单');
    console.log('空格键/P - 暂停/继续游戏');
    
    // 添加版权信息
    console.log('多游戏合集 - 基于 Phaser.js 开发');
});

// 防止页面刷新时的意外行为
window.addEventListener('beforeunload', (e) => {
    if (gameManager && gameManager.currentGame) {
        e.preventDefault();
        e.returnValue = '确定要离开吗？游戏进度将会丢失。';
    }
});

// 处理窗口大小变化
window.addEventListener('resize', () => {
    if (gameManager && gameManager.currentGame) {
        // 可以在这里添加响应式处理逻辑
        gameManager.currentGame.scale.refresh();
    }
});
