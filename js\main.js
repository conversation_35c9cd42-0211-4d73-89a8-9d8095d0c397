// 页面加载完成后初始化游戏管理器和选择器
document.addEventListener('DOMContentLoaded', () => {
    gameManager = new GameManager();
    gameManager.init();

    // 初始化游戏选择器
    gameSelector = new GameSelector();
    gameSelector.init();

    // 添加键盘快捷键提示
    console.log('游戏控制:');
    console.log('方向键/WASD - 选择游戏');
    console.log('Enter/空格键 - 开始游戏');
    console.log('ESC - 返回主菜单');
    console.log('P - 暂停/继续游戏');
    console.log('支持鼠标拖拽和手柄操作');

    // 添加版权信息
    console.log('多游戏合集 - 基于 Phaser.js 开发');
});

// 防止页面刷新时的意外行为
window.addEventListener('beforeunload', (e) => {
    if (gameManager && gameManager.currentGame) {
        e.preventDefault();
        e.returnValue = '确定要离开吗？游戏进度将会丢失。';
    }
});

// 处理窗口大小变化
window.addEventListener('resize', () => {
    if (gameManager && gameManager.currentGame) {
        // 可以在这里添加响应式处理逻辑
        gameManager.currentGame.scale.refresh();
    }
});
