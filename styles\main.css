* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

#game-container {
    text-align: center;
    max-width: 1000px;
    width: 100%;
    padding: 20px;
}

#menu-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    font-size: 3em;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

#game-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.game-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 15px 30px;
    font-size: 1.2em;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.game-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.game-btn:active {
    transform: translateY(-1px);
}

.game-btn[data-game="snake"] {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.game-btn[data-game="breakout"] {
    background: linear-gradient(45deg, #3498db, #2980b9);
}

.game-btn[data-game="shooter"] {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

#instructions {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
}

#instructions h3 {
    margin-bottom: 15px;
    color: #ffd700;
}

#game-info {
    font-size: 1.1em;
    line-height: 1.6;
}

#game-canvas-container {
    position: relative;
}

#game-ui {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 10px;
}

#score {
    font-size: 1.5em;
    font-weight: bold;
    color: #ffd700;
}

#game-controls {
    display: flex;
    gap: 10px;
}

#pause-btn, #back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#pause-btn:hover, #back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

canvas {
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

@media (max-width: 768px) {
    #game-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .game-btn {
        width: 200px;
    }
    
    h1 {
        font-size: 2em;
    }
    
    #menu-container {
        padding: 20px;
    }
}
