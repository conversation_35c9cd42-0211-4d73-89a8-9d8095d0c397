* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
}

#game-container {
    text-align: center;
    max-width: 1000px;
    width: 100%;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

#menu-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

h1 {
    font-size: 3em;
    margin-bottom: 30px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(to right, #4b7a8a, #3a3aff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 游戏选择器样式 */
#game-selector {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin: 0 auto 30px;
    overflow: hidden;
    border-radius: 20px;
}

#game-carousel {
    display: flex;
    transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    width: 400%;
}

.game-card {
    width: 25%;
    padding: 30px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    opacity: 0.7;
    transform: scale(0.9);
    transition: all 0.5s ease;
    cursor: pointer;
    user-select: none;
}

.game-card.active {
    opacity: 1;
    transform: scale(1);
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.game-card:hover {
    background: rgba(255, 255, 255, 0.2);
}

.game-icon {
    font-size: 4em;
    margin-bottom: 15px;
    display: block;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.game-card h3 {
    font-size: 1.8em;
    margin-bottom: 15px;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.game-card p {
    font-size: 1em;
    line-height: 1.6;
    margin-bottom: 15px;
    color: rgba(255, 255, 255, 0.9);
}

.game-controls {
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
}

/* 导航指示器 */
#game-indicators {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin: 20px 0;
}

.indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.indicator.active {
    background: #ffd700;
    transform: scale(1.2);
}

.indicator:hover {
    background: rgba(255, 255, 255, 0.6);
}

/* 导航按钮 */
.nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.5em;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
}

#prev-btn {
    left: 10px;
}

#next-btn {
    right: 10px;
}

.nav-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.nav-btn:active {
    transform: translateY(-50%) scale(0.95);
}

/* 开始游戏按钮 */
#start-game-container {
    margin: 30px 0;
}

.start-btn {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    border: none;
    color: white;
    padding: 15px 40px;
    font-size: 1.3em;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    font-weight: bold;
    text-transform: uppercase;
    letter-spacing: 1px;
    position: relative;
    overflow: hidden;
}

.start-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.start-btn:active {
    transform: translateY(-1px);
}

.start-btn:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* 控制提示 */
#control-hints {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 20px;
    margin-top: 20px;
    font-size: 0.9em;
    color: rgba(255, 255, 255, 0.8);
}

#control-hints p {
    margin: 5px 0;
}

#game-canvas-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px auto;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    width: fit-content;
    max-width: 90vw;
}

/* 确保Phaser canvas居中 */
#game-canvas-container canvas {
    display: block !important;
    margin: 0 auto !important;
}

#game-ui {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
    background: rgba(0, 0, 0, 0.7);
    padding: 10px 20px;
    border-radius: 10px;
}

#game-controls {
    display: flex;
    gap: 10px;
}

#pause-btn, #back-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

#pause-btn:hover, #back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

canvas {
    border-radius: 10px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    display: block;
    margin: 0 auto;
}

/* 动画效果 */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.game-card.active .game-icon {
    animation: pulse 2s ease-in-out infinite;
}

#menu-container {
    animation: fadeIn 0.8s ease-out;
}

#game-selector {
    animation: slideInUp 0.6s ease-out 0.2s both;
}

#start-game-container {
    animation: slideInUp 0.6s ease-out 0.4s both;
}

#control-hints {
    animation: slideInUp 0.6s ease-out 0.6s both;
}

/* 拖拽时的视觉反馈 */
.game-card {
    will-change: transform, opacity;
}

#game-carousel {
    will-change: transform;
}

/* 焦点样式 */
.game-card:focus,
.nav-btn:focus,
.indicator:focus {
    outline: 2px solid #ffd700;
    outline-offset: 2px;
}

/* 选中状态的额外效果 */
.game-card.active {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

@media (max-width: 768px) {
    h1 {
        font-size: 2em;
    }

    #menu-container {
        padding: 20px;
    }

    #game-selector {
        max-width: 100%;
    }

    .game-card {
        padding: 20px;
    }

    .game-icon {
        font-size: 3em;
    }

    .game-card h3 {
        font-size: 1.5em;
    }

    .nav-btn {
        width: 35px;
        height: 35px;
        font-size: 1.2em;
    }

    #prev-btn {
        left: 5px;
    }

    #next-btn {
        right: 5px;
    }

    .start-btn {
        padding: 12px 30px;
        font-size: 1.1em;
    }
}
