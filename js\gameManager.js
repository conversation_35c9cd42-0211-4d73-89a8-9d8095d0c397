class GameManager {
    constructor() {
        this.currentGame = null;
        this.gameConfig = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-canvas-container',
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: []
        };
        this.score = 0;
        this.isPaused = false;
        this.gameInstances = {};
    }

    init() {
        this.setupEventListeners();
        this.showMenu();
    }

    setupEventListeners() {
        // 控制按钮事件
        document.getElementById('pause-btn').addEventListener('click', () => {
            this.togglePause();
        });

        document.getElementById('back-btn').addEventListener('click', () => {
            this.backToMenu();
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                if (this.currentGame) {
                    this.backToMenu();
                }
            } else if (e.key === 'p' || e.key === 'P') {
                if (this.currentGame) {
                    this.togglePause();
                    e.preventDefault();
                }
            }
        });
    }

    startGame(gameType) {
        this.hideMenu();
        this.showGameCanvas();
        this.resetScore();

        // 销毁之前的游戏实例
        if (this.currentGame) {
            this.currentGame.destroy(true);
        }

        // 根据游戏类型创建对应的场景
        let gameScene;
        switch (gameType) {
            case 'snake':
                gameScene = new SnakeGame();
                this.updateGameInfo('贪吃蛇', '使用方向键控制蛇的移动，吃食物获得分数！');
                break;
            case 'breakout':
                gameScene = new BreakoutGame();
                this.updateGameInfo('打砖块', '使用鼠标或方向键控制挡板，击破所有砖块！');
                break;
            case 'shooter':
                gameScene = new ShooterGame();
                this.updateGameInfo('太空射击', '使用方向键移动，空格键射击，消灭所有敌人！');
                break;
            case 'colorguess':
                gameScene = new ColorGuessGame();
                this.updateGameInfo('猜颜色', '通过逻辑推理猜出隐藏的颜色组合！');
                break;
            default:
                console.error('未知的游戏类型:', gameType);
                return;
        }

        // 创建新的游戏配置
        const config = {
            ...this.gameConfig,
            scene: [gameScene]
        };

        // 创建游戏实例
        this.currentGame = new Phaser.Game(config);
        this.gameInstances[gameType] = this.currentGame;

        // 设置游戏管理器引用
        gameScene.gameManager = this;
    }

    updateGameInfo(title, description) {
        const gameInfo = document.getElementById('game-info');
        gameInfo.innerHTML = `
            <h4>${title}</h4>
            <p>${description}</p>
        `;
    }

    showMenu() {
        document.getElementById('menu-container').style.display = 'block';
        document.getElementById('game-canvas-container').style.display = 'none';
    }

    hideMenu() {
        document.getElementById('menu-container').style.display = 'none';
    }

    showGameCanvas() {
        document.getElementById('game-canvas-container').style.display = 'block';
    }

    backToMenu() {
        if (this.currentGame) {
            this.currentGame.destroy(true);
            this.currentGame = null;
        }
        this.showMenu();
        this.isPaused = false;
        this.updatePauseButton();
    }

    togglePause() {
        if (!this.currentGame) return;

        this.isPaused = !this.isPaused;

        if (this.isPaused) {
            this.currentGame.scene.pause();
        } else {
            this.currentGame.scene.resume();
        }

        this.updatePauseButton();
    }

    updatePauseButton() {
        const pauseBtn = document.getElementById('pause-btn');
        pauseBtn.textContent = this.isPaused ? '继续' : '暂停';
    }

    updateScore(newScore) {
        this.score = newScore;
        document.getElementById('score').textContent = `分数: ${this.score}`;
    }

    resetScore() {
        this.updateScore(0);
    }

    gameOver(finalScore, message = '游戏结束！') {
        setTimeout(() => {
            alert(`${message}\n最终分数: ${finalScore}`);
            this.backToMenu();
        }, 100);
    }
}
