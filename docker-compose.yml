version: '3.8'

services:
  games-app:
    build: .
    container_name: phaser-games
    ports:
      - "8080:80"
    restart: unless-stopped
    environment:
      - NGINX_HOST=localhost
      - NGINX_PORT=80
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.games.rule=Host(`games.localhost`)"
      - "traefik.http.services.games.loadbalancer.server.port=80"
    networks:
      - games-network

networks:
  games-network:
    driver: bridge
