class ColorGuessGame extends Phaser.Scene {
    constructor() {
        super({ key: 'ColorGuessGame' });

        // 游戏配置
        this.colors = [
            { name: 'blue', hex: 0x0066ff, display: '蓝' },
            { name: 'red', hex: 0xff0000, display: '红' },
            { name: 'green', hex: 0x00cc00, display: '绿' },
            { name: 'yellow', hex: 0xffcc00, display: '黄' },
            { name: 'purple', hex: 0xcc00cc, display: '紫' },
            { name: 'white', hex: 0xffffff, display: '白' }
        ];

        this.secretCode = [];
        this.gameRows = [];
        this.currentRowIndex = 0;
        this.selectedColorIndex = 1; // 默认选中红色（索引1）
        this.gameOver = false;
        this.isDragging = false; // 拖拽状态标记
    }

    preload() {
        // 不需要预加载任何资源
    }

    create() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        // 重置拖拽状态
        this.isDragging = false;

        // 生成密码
        this.generateSecretCode();

        // 创建背景
        this.add.rectangle(width / 2, height / 2, width, height, 0x2c3e50);

        // 创建游戏界面
        this.createGameInterface();

        // 设置输入
        this.setupInput();
    }

    generateSecretCode() {
        this.secretCode = [];
        for (let i = 0; i < 4; i++) {
            const randomIndex = Phaser.Math.Between(0, this.colors.length - 1);
            this.secretCode.push(this.colors[randomIndex]);
        }
        console.log('密码:', this.secretCode.map(c => c.display).join(', '));
    }

    createGameInterface() {
        // 创建顶部说明
        this.createTopInfo();

        // 创建主游戏板
        this.createMainBoard();

        // 创建颜色选择器
        this.createColorSelector();
    }

    createTopInfo() {
        const width = this.sys.game.config.width;

        // 顶部背景
        this.add.rectangle(width / 2, 30, width - 20, 50, 0x4a90e2);

        // 全对示例 - 居中布局
        const leftExampleX = width / 2 - 150;
        this.add.circle(leftExampleX - 50, 30, 8, 0xff4500);
        this.add.text(leftExampleX - 30, 30, '全对：颜色和位置都正确', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0, 0.5);

        // 半对示例 - 居中布局
        const rightExampleX = width / 2 + 50;
        this.add.circle(rightExampleX, 30, 8, 0xffffff);
        this.add.text(rightExampleX + 20, 30, '半对：只有颜色正确', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0, 0.5);

        // 返回菜单按钮
        const backButton = this.add.rectangle(width - 80, 30, 120, 35, 0x333333);
        backButton.setStrokeStyle(2, 0x666666);
        backButton.setInteractive();

        this.add.text(width - 80, 30, '返回菜单', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        backButton.on('pointerdown', () => {
            this.resetAllDragStates();
            this.scene.start('MainMenu');
        });

        backButton.on('pointerover', () => {
            backButton.setFillStyle(0x555555);
        });

        backButton.on('pointerout', () => {
            backButton.setFillStyle(0x333333);
        });
    }

    createMainBoard() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        // 居中计算 - 适应全屏，放大尺寸
        const boardWidth = Math.min(700, width * 0.8);  // 从500增加到700
        const boardHeight = Math.min(600, height * 0.8); // 从450增加到600
        const boardCenterX = width / 2;
        const boardCenterY = height / 2 - 30; // 稍微向上偏移

        // 放大所有元素尺寸
        const slotSize = 40;        // 从30增加到40
        const slotSpacing = 60;     // 从45增加到60
        const rowHeight = 65;       // 从50增加到65
        const boardStartX = boardCenterX - 200; // 从-150调整到-200
        const boardStartY = boardCenterY - 240; // 从-180调整到-240

        // 游戏板背景 - 更深的颜色，更像真实的游戏板
        this.add.rectangle(boardCenterX, boardCenterY, boardWidth, boardHeight, 0x2a2a2a);
        this.add.rectangle(boardCenterX, boardCenterY, boardWidth, boardHeight, 0x000000, 0).setStrokeStyle(4, 0x666666); // 边框也加粗

        this.gameRows = [];

        for (let row = 0; row < 8; row++) {
            const y = boardStartY + row * rowHeight;

            // 行号 - 放大字体
            this.add.text(boardStartX - 40, y, (row + 1).toString(), {
                fontSize: '32px',  // 从24px增加到32px
                fill: '#ffffff',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 箭头 - 放大字体和调整位置
            this.add.text(boardStartX + 260, y, '▶', {
                fontSize: '24px',  // 从16px增加到24px
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 4个猜测槽位
            const guessSlots = [];
            for (let col = 0; col < 4; col++) {
                const x = boardStartX + col * slotSpacing;
                const slot = this.add.circle(x, y, slotSize / 2, 0x606060);
                slot.setStrokeStyle(3, 0x808080);  // 从2增加到3
                slot.setInteractive();

                // 点击槽位放置颜色
                slot.on('pointerdown', () => {
                    if (row === this.currentRowIndex) {
                        this.placeColorInSlot(row, col);
                    }
                });

                // 拖拽悬停效果
                slot.on('pointerover', () => {
                    if (row === this.currentRowIndex) {
                        slot.setStrokeStyle(4, 0xffd700);  // 从3增加到4
                    }
                });

                slot.on('pointerout', () => {
                    if (row === this.currentRowIndex) {
                        slot.setStrokeStyle(3, 0x808080);  // 从2增加到3
                    }
                });

                guessSlots.push({
                    sprite: slot,
                    color: null,
                    x: x,
                    y: y
                });
            }

            // OK按钮和提示区域 - 放大尺寸和调整位置
            const okButton = this.add.rectangle(boardStartX + 330, y, 80, 40, 0x4CAF50);  // 从60x30增加到80x40，位置从235调整到320
            okButton.setStrokeStyle(3, 0x45a049);  // 从2增加到3
            okButton.setInteractive();

            // 只有当前行显示OK按钮
            okButton.setVisible(row === this.currentRowIndex);

            const okText = this.add.text(boardStartX + 330, y, 'OK', {
                fontSize: '20px',  // 从16px增加到20px
                fill: '#ffffff',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 只有当前行显示OK文本
            okText.setVisible(row === this.currentRowIndex);

            // 提示区域（2x2小圆点）- 初始隐藏，放大尺寸
            const hintSlots = [];
            for (let hintRow = 0; hintRow < 2; hintRow++) {
                for (let hintCol = 0; hintCol < 2; hintCol++) {
                    const hintX = boardStartX + 290 + hintCol * 20;  // 位置从220调整到290，间距从15增加到20
                    const hintY = y - 12 + hintRow * 20;  // 间距从15增加到20
                    const hintSlot = this.add.circle(hintX, hintY, 8, 0x303030);  // 半径从6增加到8
                    hintSlot.setStrokeStyle(2, 0x505050);  // 边框从1增加到2
                    hintSlot.setVisible(false); // 初始隐藏
                    hintSlots.push(hintSlot);
                }
            }

            // OK按钮点击事件
            okButton.on('pointerdown', () => {
                if (row === this.currentRowIndex && this.isRowComplete(row)) {
                    this.submitGuess(row);
                }
            });

            okButton.on('pointerover', () => {
                if (row === this.currentRowIndex && this.isRowComplete(row)) {
                    okButton.setFillStyle(0x66BB6A);
                }
            });

            okButton.on('pointerout', () => {
                okButton.setFillStyle(0x4CAF50);
            });

            this.gameRows.push({
                guessSlots: guessSlots,
                hintSlots: hintSlots,
                okButton: okButton,
                okText: okText,
                completed: false
            });
        }

        // 高亮当前行
        this.highlightCurrentRow();
    }

    createColorSelector() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        const selectorY = height - 100; // 距离底部100像素，适应放大的界面
        const selectorCenterX = width / 2;
        const spacing = 80;  // 从60增加到80
        const totalWidth = (this.colors.length - 1) * spacing;
        const startX = selectorCenterX - totalWidth / 2;

        // 选择器背景 - 放大尺寸
        this.add.rectangle(selectorCenterX, selectorY, Math.min(550, width * 0.8), 100, 0x505050);  // 从400x80增加到550x100

        this.colorButtons = [];
        this.colors.forEach((color, index) => {
            const x = startX + index * spacing;

            // 颜色球 - 放大尺寸
            const colorBall = this.add.circle(x, selectorY, 28, color.hex);  // 从20增加到28
            colorBall.setStrokeStyle(3, 0xffffff);  // 从2增加到3
            colorBall.setInteractive();

            // 选中指示器 - 放大尺寸
            const selector = this.add.circle(x, selectorY, 35, 0xffffff, 0);  // 从25增加到35
            selector.setStrokeStyle(4, 0xffd700);  // 从3增加到4
            selector.setVisible(index === 1); // 默认选中红色

            // 设置拖拽功能
            this.setupColorDrag(colorBall, color, index);

            // 点击选择颜色
            colorBall.on('pointerdown', () => {
                this.selectColor(index);
            });

            this.colorButtons.push({
                ball: colorBall,
                selector: selector,
                color: color,
                originalX: x,
                originalY: selectorY
            });
        });
    }

    setupColorDrag(colorBall, color, colorIndex) {
        // 直接设置颜色球为可拖拽，不创建副本
        colorBall.setData('originalX', colorBall.x);
        colorBall.setData('originalY', colorBall.y);
        colorBall.setData('color', color);
        colorBall.setData('colorIndex', colorIndex);

        // 开始拖拽
        colorBall.on('dragstart', () => {
            // 防止多重拖拽
            if (this.isDragging) return;

            this.isDragging = true;

            // 设置拖拽时的视觉效果
            colorBall.setAlpha(0.8);
            colorBall.setDepth(1000);
            colorBall.setScale(1.1);

            // 选择这个颜色
            this.selectColor(colorIndex);
        });

        // 拖拽中
        colorBall.on('drag', (pointer, dragX, dragY) => {
            if (this.isDragging) {
                colorBall.x = dragX;
                colorBall.y = dragY;
            }
        });

        // 结束拖拽
        colorBall.on('dragend', (pointer) => {
            if (!this.isDragging) return;

            // 检查是否拖拽到了有效的槽位
            const dropSlot = this.findDropSlot(pointer.x, pointer.y);

            if (dropSlot) {
                // 获取被拖拽球的颜色信息
                const draggedColor = colorBall.getData('color');
                const draggedColorIndex = colorBall.getData('colorIndex');

                // 放置被拖拽的颜色到槽位
                this.placeColorInSlotWithColor(dropSlot.row, dropSlot.col, draggedColor, draggedColorIndex);
            }

            // 恢复颜色球到原位置和状态
            this.resetColorBall(colorBall);
        });

        // 启用拖拽
        this.input.setDraggable(colorBall);
    }

    resetColorBall(colorBall) {
        // 恢复颜色球的位置和状态
        const originalX = colorBall.getData('originalX');
        const originalY = colorBall.getData('originalY');

        colorBall.x = originalX;
        colorBall.y = originalY;
        colorBall.setAlpha(1);
        colorBall.setDepth(0);
        colorBall.setScale(1);

        this.isDragging = false;
    }

    resetAllDragStates() {
        // 重置所有颜色球的拖拽状态
        if (this.colorButtons) {
            this.colorButtons.forEach(button => {
                if (button.ball && button.ball.active) {
                    this.resetColorBall(button.ball);
                }
            });
        }
        this.isDragging = false;
    }



    findDropSlot(x, y) {
        const currentRow = this.gameRows[this.currentRowIndex];
        if (!currentRow || this.gameOver) return null;

        // 检查每个槽位
        for (let col = 0; col < currentRow.guessSlots.length; col++) {
            const slot = currentRow.guessSlots[col];
            const distance = Phaser.Math.Distance.Between(x, y, slot.x, slot.y);

            // 如果距离小于槽位半径的1.5倍，认为是有效拖拽目标
            if (distance < 45) {
                return {
                    row: this.currentRowIndex,
                    col: col,
                    x: slot.x,
                    y: slot.y
                };
            }
        }

        return null;
    }

    isRowComplete(rowIndex) {
        const row = this.gameRows[rowIndex];
        if (!row) return false;

        // 检查所有槽位是否都有颜色
        return row.guessSlots.every(slot => slot.color !== null);
    }

    submitGuess(rowIndex) {
        if (this.gameOver || rowIndex !== this.currentRowIndex) return;

        const row = this.gameRows[rowIndex];
        const guess = row.guessSlots.map(slot => slot.color);

        // 隐藏OK按钮和文本
        row.okButton.setVisible(false);
        row.okText.setVisible(false);

        // 显示提示区域
        row.hintSlots.forEach(slot => slot.setVisible(true));

        // 计算提示
        const hints = this.calculateHints(guess);
        this.displayHints(row, hints);

        // 标记行为已完成
        row.completed = true;

        // 检查游戏结果
        if (this.checkWin(guess)) {
            this.gameOver = true;
            setTimeout(() => {
                alert('恭喜！你猜对了！');
                this.resetAllDragStates();
                this.scene.start('MainMenu');
            }, 500);
        } else if (this.currentRowIndex >= 7) {
            this.gameOver = true;
            setTimeout(() => {
                alert(`游戏结束！正确答案是：${this.secretCode.map(c => c.name).join(', ')}`);
                this.resetAllDragStates();
                this.scene.start('MainMenu');
            }, 500);
        } else {
            // 移动到下一行
            this.currentRowIndex++;
            this.highlightCurrentRow();
        }
    }

    highlightCurrentRow() {
        // 可以添加当前行高亮效果
        // 更新当前行OK按钮的可见性
        this.updateOkButtonVisibility();
    }

    updateOkButtonVisibility() {
        this.gameRows.forEach((row, index) => {
            if (index === this.currentRowIndex && !row.completed) {
                // 当前行：显示OK按钮，根据完成状态调整透明度
                const isComplete = this.isRowComplete(index);
                row.okButton.setVisible(true);
                row.okText.setVisible(true);
                row.okButton.setAlpha(isComplete ? 1 : 0.5);
                row.okText.setAlpha(isComplete ? 1 : 0.5);
            } else {
                // 其他行：隐藏OK按钮
                row.okButton.setVisible(false);
                row.okText.setVisible(false);
            }
        });
    }

    selectColor(index) {
        // 隐藏之前的选择
        this.colorButtons[this.selectedColorIndex].selector.setVisible(false);

        // 显示新选择
        this.selectedColorIndex = index;
        this.colorButtons[index].selector.setVisible(true);
    }

    placeColorInSlot(row, col) {
        if (this.gameOver || row !== this.currentRowIndex) return;

        const selectedColor = this.colors[this.selectedColorIndex];
        this.placeColorInSlotWithColor(row, col, selectedColor, this.selectedColorIndex);
    }

    placeColorInSlotWithColor(row, col, color, colorIndex) {
        if (this.gameOver || row !== this.currentRowIndex) return;

        const slot = this.gameRows[row].guessSlots[col];

        // 设置颜色
        slot.sprite.setFillStyle(color.hex);
        slot.color = color;

        // 动画效果
        this.tweens.add({
            targets: slot.sprite,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 100,
            yoyo: true
        });

        // 更新选中的颜色索引（这样拖拽后会自动选中该颜色）
        this.selectColor(colorIndex);

        // 更新OK按钮状态
        this.updateOkButtonVisibility();
    }



    calculateHints(guess) {
        let exact = 0;
        let color = 0;

        const secretCopy = [...this.secretCode];
        const guessCopy = [...guess];

        // 计算完全匹配
        for (let i = 0; i < 4; i++) {
            if (guessCopy[i] && guessCopy[i].name === secretCopy[i].name) {
                exact++;
                secretCopy[i] = null;
                guessCopy[i] = null;
            }
        }

        // 计算颜色匹配
        for (let i = 0; i < 4; i++) {
            if (guessCopy[i]) {
                for (let j = 0; j < 4; j++) {
                    if (secretCopy[j] && guessCopy[i].name === secretCopy[j].name) {
                        color++;
                        secretCopy[j] = null;
                        break;
                    }
                }
            }
        }

        return { exact, color };
    }

    checkWin(guess) {
        return guess.every((color, index) => color.name === this.secretCode[index].name);
    }

    displayHints(row, hints) {
        let hintIndex = 0;

        // 显示完全匹配（橙色）
        for (let i = 0; i < hints.exact; i++) {
            row.hintSlots[hintIndex].setFillStyle(0xff4500);
            hintIndex++;
        }

        // 显示颜色匹配（白色）
        for (let i = 0; i < hints.color; i++) {
            row.hintSlots[hintIndex].setFillStyle(0xffffff);
            hintIndex++;
        }
    }

    setupInput() {
        this.input.keyboard.on('keydown', (event) => {
            if (this.gameOver) return;

            switch (event.key) {
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                    const colorIndex = parseInt(event.key) - 1;
                    if (colorIndex < this.colors.length) {
                        this.selectColor(colorIndex);
                    }
                    break;
                case ' ':
                    // 空格键自动放置到下一个空位
                    this.placeColorInNextSlot();
                    break;
                case 'Enter':
                    if (this.isRowComplete(this.currentRowIndex)) {
                        this.submitGuess(this.currentRowIndex);
                    }
                    break;
            }
        });
    }

    placeColorInNextSlot() {
        const currentRow = this.gameRows[this.currentRowIndex];
        const emptySlot = currentRow.guessSlots.findIndex(slot => slot.color === null);

        if (emptySlot !== -1) {
            this.placeColorInSlot(this.currentRowIndex, emptySlot);
        }
    }

    gameWon() {
        this.gameOver = true;
        this.add.text(400, 250, '恭喜！你猜对了！', {
            fontSize: '32px',
            fill: '#00ff00',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.time.delayedCall(2000, () => {
            this.resetAllDragStates();
            this.scene.start('MainMenu');
        });
    }

    gameLost() {
        this.gameOver = true;
        this.add.text(400, 250, '游戏结束！', {
            fontSize: '32px',
            fill: '#ff0000',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 显示答案
        this.add.text(400, 290, '答案是：' + this.secretCode.map(c => c.display).join(' '), {
            fontSize: '20px',
            fill: '#ffffff'
        }).setOrigin(0.5);

        this.time.delayedCall(3000, () => {
            this.resetAllDragStates();
            this.scene.start('MainMenu');
        });
    }
}
