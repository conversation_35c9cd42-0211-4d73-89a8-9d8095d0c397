class ColorGuessGame extends Phaser.Scene {
    constructor() {
        super({ key: 'ColorGuessGame' });

        // 游戏配置
        this.colors = [
            { name: 'red', hex: 0xff0000, display: '红' },
            { name: 'blue', hex: 0x0066ff, display: '蓝' },
            { name: 'green', hex: 0x00ff00, display: '绿' },
            { name: 'yellow', hex: 0xffff00, display: '黄' },
            { name: 'purple', hex: 0x9900ff, display: '紫' },
            { name: 'orange', hex: 0xff6600, display: '橙' }
        ];

        this.secretCode = [];
        this.currentGuess = [];
        this.guessHistory = [];
        this.currentRound = 1;
        this.maxRounds = 8;
        this.codeLength = 4;
        this.gameOver = false;
        this.selectedColorIndex = 0;

        // UI元素
        this.colorPalette = [];
        this.guessSlots = [];
        this.historyRows = [];
        this.submitButton = null;
        this.clearButton = null;
    }

    preload() {
        // 不需要预加载任何资源，直接使用Phaser的图形API
    }

    create() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        // 生成密码
        this.generateSecretCode();

        // 创建背景
        this.add.rectangle(width / 2, height / 2, width, height, 0x2c3e50);

        // 创建标题
        this.add.text(width / 2, 30, '猜颜色游戏', {
            fontSize: '28px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 创建游戏说明
        this.add.text(width / 2, 60, '猜出4个颜色的正确组合和位置', {
            fontSize: '16px',
            fill: '#cccccc'
        }).setOrigin(0.5);

        // 创建轮次显示
        this.roundText = this.add.text(width / 2, 90, `第 ${this.currentRound} / ${this.maxRounds} 轮`, {
            fontSize: '18px',
            fill: '#ffd700'
        }).setOrigin(0.5);

        // 创建颜色选择面板
        this.createColorPalette();

        // 创建当前猜测区域
        this.createGuessArea();

        // 创建历史记录区域
        this.createHistoryArea();

        // 创建控制按钮
        this.createControlButtons();

        // 设置键盘输入
        this.setupKeyboardInput();

        // 显示游戏规则
        this.showGameRules();
    }

    hexToString(hex) {
        return '#' + hex.toString(16).padStart(6, '0');
    }

    generateSecretCode() {
        this.secretCode = [];
        for (let i = 0; i < this.codeLength; i++) {
            const randomIndex = Phaser.Math.Between(0, this.colors.length - 1);
            this.secretCode.push(this.colors[randomIndex]);
        }
        console.log('密码已生成:', this.secretCode.map(c => c.display).join(', '));
    }

    createColorPalette() {
        const startX = 100;
        const startY = 150;
        const spacing = 70;

        this.add.text(50, startY - 20, '选择颜色:', {
            fontSize: '16px',
            fill: '#ffffff'
        });

        this.colorPalette = [];
        this.colors.forEach((color, index) => {
            const x = startX + (index % 3) * spacing;
            const y = startY + Math.floor(index / 3) * spacing;

            // 使用Phaser的图形API创建颜色圆圈
            const colorCircle = this.add.circle(x, y, 20, color.hex);
            colorCircle.setStrokeStyle(2, 0x333333);
            colorCircle.setInteractive();

            // 添加颜色名称标签
            this.add.text(x, y + 35, color.display, {
                fontSize: '12px',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 选中边框
            const border = this.add.circle(x, y, 25, 0xffffff, 0);
            border.setStrokeStyle(3, 0xffd700);
            border.setVisible(index === this.selectedColorIndex);

            colorCircle.on('pointerdown', () => {
                this.selectColor(index);
            });

            colorCircle.on('pointerover', () => {
                colorCircle.setScale(1.1);
            });

            colorCircle.on('pointerout', () => {
                colorCircle.setScale(1.0);
            });

            this.colorPalette.push({
                sprite: colorCircle,
                border: border,
                color: color
            });
        });
    }

    createGuessArea() {
        const startX = 400;
        const startY = 180;
        const spacing = 60;

        this.add.text(startX - 50, startY - 30, '当前猜测:', {
            fontSize: '16px',
            fill: '#ffffff'
        });

        this.guessSlots = [];
        for (let i = 0; i < this.codeLength; i++) {
            const x = startX + i * spacing;
            const y = startY;

            // 创建空槽位（虚线圆圈）
            const slot = this.add.circle(x, y, 20, 0xf0f0f0, 0);
            slot.setStrokeStyle(2, 0x999999, 1, 0.5);
            slot.setInteractive();

            slot.on('pointerdown', () => {
                this.placeColorInSlot(i);
            });

            // 位置标签
            this.add.text(x, y + 40, `${i + 1}`, {
                fontSize: '14px',
                fill: '#cccccc'
            }).setOrigin(0.5);

            this.guessSlots.push({
                sprite: slot,
                color: null,
                index: i
            });
        }
    }

    createHistoryArea() {
        const startX = 50;
        const startY = 280;

        this.add.text(startX, startY - 20, '猜测历史:', {
            fontSize: '16px',
            fill: '#ffffff'
        });

        this.historyContainer = this.add.container(0, 0);
    }

    createControlButtons() {
        const buttonY = 240;

        // 提交按钮
        this.submitButton = this.add.rectangle(500, buttonY, 80, 35, 0x00aa00);
        this.submitButton.setStrokeStyle(2, 0x00ff00);
        this.submitButton.setInteractive();

        const submitText = this.add.text(500, buttonY, '提交', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.submitButton.on('pointerdown', () => {
            this.submitGuess();
        });

        this.submitButton.on('pointerover', () => {
            this.submitButton.setFillStyle(0x00cc00);
        });

        this.submitButton.on('pointerout', () => {
            this.submitButton.setFillStyle(0x00aa00);
        });

        // 清空按钮
        this.clearButton = this.add.rectangle(600, buttonY, 80, 35, 0xaa0000);
        this.clearButton.setStrokeStyle(2, 0xff0000);
        this.clearButton.setInteractive();

        const clearText = this.add.text(600, buttonY, '清空', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.clearButton.on('pointerdown', () => {
            this.clearGuess();
        });

        this.clearButton.on('pointerover', () => {
            this.clearButton.setFillStyle(0xcc0000);
        });

        this.clearButton.on('pointerout', () => {
            this.clearButton.setFillStyle(0xaa0000);
        });
    }

    setupKeyboardInput() {
        this.input.keyboard.on('keydown', (event) => {
            if (this.gameOver) return;

            switch (event.key) {
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                    const colorIndex = parseInt(event.key) - 1;
                    if (colorIndex < this.colors.length) {
                        this.selectColor(colorIndex);
                    }
                    break;
                case 'Enter':
                    this.submitGuess();
                    break;
                case 'Backspace':
                case 'Delete':
                    this.clearGuess();
                    break;
                case 'ArrowLeft':
                    this.selectedColorIndex = Math.max(0, this.selectedColorIndex - 1);
                    this.updateColorSelection();
                    break;
                case 'ArrowRight':
                    this.selectedColorIndex = Math.min(this.colors.length - 1, this.selectedColorIndex + 1);
                    this.updateColorSelection();
                    break;
            }
        });
    }

    selectColor(index) {
        this.selectedColorIndex = index;
        this.updateColorSelection();

        // 自动放置到下一个空槽位
        const emptySlot = this.guessSlots.find(slot => slot.color === null);
        if (emptySlot) {
            this.placeColorInSlot(emptySlot.index);
        }
    }

    updateColorSelection() {
        this.colorPalette.forEach((item, index) => {
            item.border.setVisible(index === this.selectedColorIndex);
        });
    }

    placeColorInSlot(slotIndex) {
        if (this.gameOver) return;

        const slot = this.guessSlots[slotIndex];
        const selectedColor = this.colors[this.selectedColorIndex];

        // 更新槽位颜色
        slot.sprite.setFillStyle(selectedColor.hex);
        slot.sprite.setStrokeStyle(2, 0x333333);
        slot.color = selectedColor;

        // 添加放置效果
        this.tweens.add({
            targets: slot.sprite,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 100,
            yoyo: true
        });
    }

    clearGuess() {
        this.guessSlots.forEach(slot => {
            slot.sprite.setFillStyle(0xf0f0f0, 0);
            slot.sprite.setStrokeStyle(2, 0x999999, 1, 0.5);
            slot.color = null;
        });
    }

    submitGuess() {
        if (this.gameOver) return;

        // 检查是否所有槽位都已填充
        const currentGuess = this.guessSlots.map(slot => slot.color).filter(color => color !== null);
        if (currentGuess.length !== this.codeLength) {
            this.showMessage('请填满所有4个位置！', 0xff0000);
            return;
        }

        // 计算提示
        const hints = this.calculateHints(currentGuess);

        // 添加到历史记录
        this.addToHistory(currentGuess, hints);

        // 检查胜利条件
        if (hints.correct === this.codeLength) {
            this.gameWin();
            return;
        }

        // 检查是否用完所有轮次
        if (this.currentRound >= this.maxRounds) {
            this.gameLose();
            return;
        }

        // 下一轮
        this.nextRound();
    }

    calculateHints(guess) {
        const secretCopy = [...this.secretCode];
        const guessCopy = [...guess];
        let correct = 0;
        let partial = 0;

        // 首先计算完全正确的（位置和颜色都对）
        for (let i = guessCopy.length - 1; i >= 0; i--) {
            if (guessCopy[i].name === secretCopy[i].name) {
                correct++;
                guessCopy.splice(i, 1);
                secretCopy.splice(i, 1);
            }
        }

        // 然后计算部分正确的（颜色对但位置错）
        for (let i = 0; i < guessCopy.length; i++) {
            const secretIndex = secretCopy.findIndex(color => color.name === guessCopy[i].name);
            if (secretIndex !== -1) {
                partial++;
                secretCopy.splice(secretIndex, 1);
            }
        }

        return { correct, partial };
    }

    addToHistory(guess, hints) {
        const historyY = 300 + this.guessHistory.length * 50;
        const startX = 50;

        // 轮次标签
        this.add.text(startX, historyY, `${this.currentRound}:`, {
            fontSize: '14px',
            fill: '#ffffff'
        });

        // 猜测的颜色
        guess.forEach((color, index) => {
            const x = startX + 30 + index * 35;
            const colorCircle = this.add.circle(x, historyY, 12, color.hex);
            colorCircle.setStrokeStyle(1, 0x333333);
        });

        // 提示点
        const hintStartX = startX + 30 + this.codeLength * 35 + 20;
        for (let i = 0; i < hints.correct; i++) {
            const correctHint = this.add.circle(hintStartX + i * 20, historyY, 8, 0x00ff00);
            correctHint.setStrokeStyle(1, 0x333333);
        }
        for (let i = 0; i < hints.partial; i++) {
            const partialHint = this.add.circle(hintStartX + (hints.correct + i) * 20, historyY, 8, 0xffff00);
            partialHint.setStrokeStyle(1, 0x333333);
        }

        // 提示文字
        this.add.text(hintStartX + 100, historyY, `全对:${hints.correct} 半对:${hints.partial}`, {
            fontSize: '12px',
            fill: '#cccccc'
        });

        this.guessHistory.push({ guess, hints });
    }

    nextRound() {
        this.currentRound++;
        this.roundText.setText(`第 ${this.currentRound} / ${this.maxRounds} 轮`);
        this.clearGuess();
    }

    gameWin() {
        this.gameOver = true;

        this.showMessage('恭喜！你猜对了！', 0x00ff00);

        setTimeout(() => {
            this.gameManager.gameOver(`猜颜色游戏胜利！\n用了 ${this.currentRound} 轮猜中答案`);
        }, 2000);
    }

    gameLose() {
        this.gameOver = true;

        // 显示正确答案
        this.add.text(400, 120, '正确答案:', {
            fontSize: '16px',
            fill: '#ff0000'
        });

        this.secretCode.forEach((color, index) => {
            const x = 400 + index * 50;
            const y = 150;
            const answerCircle = this.add.circle(x, y, 20, color.hex);
            answerCircle.setStrokeStyle(2, 0x333333);
        });

        this.showMessage('游戏结束！未能在8轮内猜中', 0xff0000);

        setTimeout(() => {
            this.gameManager.gameOver('猜颜色游戏结束！');
        }, 3000);
    }

    showMessage(text, color) {
        const message = this.add.text(400, 400, text, {
            fontSize: '20px',
            fill: this.hexToString(color),
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: message,
            alpha: 0,
            duration: 2000,
            onComplete: () => {
                message.destroy();
            }
        });
    }

    showGameRules() {
        const rules = [
            '游戏规则:',
            '1. 系统生成4个颜色的密码',
            '2. 你有8轮机会猜测',
            '3. 绿点=颜色和位置都对',
            '4. 黄点=颜色对但位置错',
            '5. 数字键1-6选择颜色',
            '6. Enter提交，Delete清空'
        ];

        rules.forEach((rule, index) => {
            this.add.text(550, 300 + index * 20, rule, {
                fontSize: '12px',
                fill: '#cccccc'
            });
        });
    }
}
