class ColorGuessGame extends Phaser.Scene {
    constructor() {
        super({ key: 'ColorGuessGame' });

        // 游戏配置
        this.colors = [
            { name: 'blue', hex: 0x0066ff, display: '蓝' },
            { name: 'red', hex: 0xff0000, display: '红' },
            { name: 'green', hex: 0x00cc00, display: '绿' },
            { name: 'yellow', hex: 0xffcc00, display: '黄' },
            { name: 'purple', hex: 0xcc00cc, display: '紫' },
            { name: 'white', hex: 0xffffff, display: '白' }
        ];

        this.secretCode = [];
        this.gameRows = [];
        this.currentRowIndex = 0;
        this.selectedColorIndex = 2; // 默认选中绿色（索引2）
        this.gameOver = false;
    }

    preload() {
        // 不需要预加载任何资源
    }

    create() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        // 生成密码
        this.generateSecretCode();

        // 创建背景
        this.add.rectangle(width / 2, height / 2, width, height, 0x2c3e50);

        // 创建游戏界面
        this.createGameInterface();

        // 设置输入
        this.setupInput();
    }

    generateSecretCode() {
        this.secretCode = [];
        for (let i = 0; i < 4; i++) {
            const randomIndex = Phaser.Math.Between(0, this.colors.length - 1);
            this.secretCode.push(this.colors[randomIndex]);
        }
        console.log('密码:', this.secretCode.map(c => c.display).join(', '));
    }

    createGameInterface() {
        // 创建顶部说明
        this.createTopInfo();

        // 创建主游戏板
        this.createMainBoard();

        // 创建颜色选择器
        this.createColorSelector();
    }

    createTopInfo() {
        const width = this.sys.game.config.width;

        // 顶部背景
        this.add.rectangle(width / 2, 30, width - 20, 50, 0x4a90e2);

        // 全对示例 - 居中布局
        const leftExampleX = width / 2 - 150;
        this.add.circle(leftExampleX - 50, 30, 8, 0xff4500);
        this.add.text(leftExampleX - 30, 30, '全对：颜色和位置都正确', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0, 0.5);

        // 半对示例 - 居中布局
        const rightExampleX = width / 2 + 50;
        this.add.circle(rightExampleX, 30, 8, 0xffffff);
        this.add.text(rightExampleX + 20, 30, '半对：只有颜色正确', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0, 0.5);

        // 返回菜单按钮
        const backButton = this.add.rectangle(width - 80, 30, 120, 35, 0x333333);
        backButton.setStrokeStyle(2, 0x666666);
        backButton.setInteractive();

        this.add.text(width - 80, 30, '返回菜单', {
            fontSize: '16px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        backButton.on('pointerdown', () => {
            this.scene.start('MainMenu');
        });

        backButton.on('pointerover', () => {
            backButton.setFillStyle(0x555555);
        });

        backButton.on('pointerout', () => {
            backButton.setFillStyle(0x333333);
        });
    }

    createMainBoard() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        // 居中计算
        const boardWidth = 400;
        const boardHeight = 420;
        const boardCenterX = width / 2;
        const boardCenterY = height / 2 - 20; // 稍微向上偏移

        const slotSize = 30;
        const slotSpacing = 45;
        const rowHeight = 50;
        const boardStartX = boardCenterX - 150; // 相对于中心的偏移
        const boardStartY = boardCenterY - 180; // 相对于中心的偏移

        // 游戏板背景 - 更深的颜色，更像真实的游戏板
        this.add.rectangle(boardCenterX, boardCenterY, boardWidth, boardHeight, 0x2a2a2a);
        this.add.rectangle(boardCenterX, boardCenterY, boardWidth, boardHeight, 0x000000, 0).setStrokeStyle(3, 0x666666);

        this.gameRows = [];

        for (let row = 0; row < 8; row++) {
            const y = boardStartY + row * rowHeight;

            // 行号
            this.add.text(boardStartX - 30, y, (row + 1).toString(), {
                fontSize: '24px',
                fill: '#ffffff',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 箭头
            if (row < 7) {
                this.add.text(boardStartX + 200, y, '▶', {
                    fontSize: '16px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
            }

            // 4个猜测槽位
            const guessSlots = [];
            for (let col = 0; col < 4; col++) {
                const x = boardStartX + col * slotSpacing;
                const slot = this.add.circle(x, y, slotSize / 2, 0x606060);
                slot.setStrokeStyle(2, 0x808080);
                slot.setInteractive();

                // 点击槽位放置颜色
                slot.on('pointerdown', () => {
                    if (row === this.currentRowIndex) {
                        this.placeColorInSlot(row, col);
                    }
                });

                guessSlots.push({
                    sprite: slot,
                    color: null,
                    x: x,
                    y: y
                });
            }

            // 提示区域（2x2小圆点）
            const hintSlots = [];
            for (let hintRow = 0; hintRow < 2; hintRow++) {
                for (let hintCol = 0; hintCol < 2; hintCol++) {
                    const hintX = boardStartX + 220 + hintCol * 15;
                    const hintY = y - 10 + hintRow * 15;
                    const hintSlot = this.add.circle(hintX, hintY, 6, 0x303030);
                    hintSlot.setStrokeStyle(1, 0x505050);
                    hintSlots.push(hintSlot);
                }
            }

            this.gameRows.push({
                guessSlots: guessSlots,
                hintSlots: hintSlots,
                completed: false
            });
        }

        // 高亮当前行
        this.highlightCurrentRow();
    }

    createColorSelector() {
        const width = this.sys.game.config.width;
        const height = this.sys.game.config.height;

        const selectorY = height - 100; // 距离底部100像素
        const selectorCenterX = width / 2;
        const spacing = 60;
        const totalWidth = (this.colors.length - 1) * spacing;
        const startX = selectorCenterX - totalWidth / 2;

        // 选择器背景
        this.add.rectangle(selectorCenterX, selectorY, 400, 80, 0x505050);

        this.colorButtons = [];
        this.colors.forEach((color, index) => {
            const x = startX + index * spacing;

            // 颜色球
            const colorBall = this.add.circle(x, selectorY, 20, color.hex);
            colorBall.setStrokeStyle(2, 0xffffff);
            colorBall.setInteractive();

            // 选中指示器
            const selector = this.add.circle(x, selectorY, 25, 0xffffff, 0);
            selector.setStrokeStyle(3, 0xffd700);
            selector.setVisible(index === 2); // 默认选中绿色

            colorBall.on('pointerdown', () => {
                this.selectColor(index);
            });

            this.colorButtons.push({
                ball: colorBall,
                selector: selector,
                color: color
            });
        });
    }

    highlightCurrentRow() {
        // 可以添加当前行高亮效果
    }

    selectColor(index) {
        // 隐藏之前的选择
        this.colorButtons[this.selectedColorIndex].selector.setVisible(false);

        // 显示新选择
        this.selectedColorIndex = index;
        this.colorButtons[index].selector.setVisible(true);
    }

    placeColorInSlot(row, col) {
        if (this.gameOver || row !== this.currentRowIndex) return;

        const slot = this.gameRows[row].guessSlots[col];
        const selectedColor = this.colors[this.selectedColorIndex];

        // 设置颜色
        slot.sprite.setFillStyle(selectedColor.hex);
        slot.color = selectedColor;

        // 动画效果
        this.tweens.add({
            targets: slot.sprite,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 100,
            yoyo: true
        });

        // 检查是否可以提交
        this.checkCanSubmit();
    }

    checkCanSubmit() {
        const currentRow = this.gameRows[this.currentRowIndex];
        const allFilled = currentRow.guessSlots.every(slot => slot.color !== null);

        if (allFilled) {
            // 自动提交
            this.submitGuess();
        }
    }

    submitGuess() {
        if (this.gameOver) return;

        const currentRow = this.gameRows[this.currentRowIndex];
        const guess = currentRow.guessSlots.map(slot => slot.color);

        // 检查是否所有位置都有颜色
        if (guess.some(color => color === null)) {
            return;
        }

        // 计算提示
        const hints = this.calculateHints(guess);
        this.displayHints(currentRow, hints);

        // 检查是否获胜
        if (hints.exact === 4) {
            this.gameWon();
            return;
        }

        // 移动到下一行
        this.currentRowIndex++;
        if (this.currentRowIndex >= 8) {
            this.gameLost();
        } else {
            this.highlightCurrentRow();
        }
    }

    calculateHints(guess) {
        let exact = 0;
        let color = 0;

        const secretCopy = [...this.secretCode];
        const guessCopy = [...guess];

        // 计算完全匹配
        for (let i = 0; i < 4; i++) {
            if (guessCopy[i] && guessCopy[i].name === secretCopy[i].name) {
                exact++;
                secretCopy[i] = null;
                guessCopy[i] = null;
            }
        }

        // 计算颜色匹配
        for (let i = 0; i < 4; i++) {
            if (guessCopy[i]) {
                for (let j = 0; j < 4; j++) {
                    if (secretCopy[j] && guessCopy[i].name === secretCopy[j].name) {
                        color++;
                        secretCopy[j] = null;
                        break;
                    }
                }
            }
        }

        return { exact, color };
    }

    displayHints(row, hints) {
        let hintIndex = 0;

        // 显示完全匹配（橙色）
        for (let i = 0; i < hints.exact; i++) {
            row.hintSlots[hintIndex].setFillStyle(0xff4500);
            hintIndex++;
        }

        // 显示颜色匹配（白色）
        for (let i = 0; i < hints.color; i++) {
            row.hintSlots[hintIndex].setFillStyle(0xffffff);
            hintIndex++;
        }
    }

    setupInput() {
        this.input.keyboard.on('keydown', (event) => {
            if (this.gameOver) return;

            switch (event.key) {
                case '1':
                case '2':
                case '3':
                case '4':
                case '5':
                case '6':
                    const colorIndex = parseInt(event.key) - 1;
                    if (colorIndex < this.colors.length) {
                        this.selectColor(colorIndex);
                    }
                    break;
                case ' ':
                    // 空格键自动放置到下一个空位
                    this.placeColorInNextSlot();
                    break;
                case 'Enter':
                    this.submitGuess();
                    break;
            }
        });
    }

    placeColorInNextSlot() {
        const currentRow = this.gameRows[this.currentRowIndex];
        const emptySlot = currentRow.guessSlots.findIndex(slot => slot.color === null);

        if (emptySlot !== -1) {
            this.placeColorInSlot(this.currentRowIndex, emptySlot);
        }
    }

    gameWon() {
        this.gameOver = true;
        this.add.text(400, 250, '恭喜！你猜对了！', {
            fontSize: '32px',
            fill: '#00ff00',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.time.delayedCall(2000, () => {
            this.scene.start('MainMenu');
        });
    }

    gameLost() {
        this.gameOver = true;
        this.add.text(400, 250, '游戏结束！', {
            fontSize: '32px',
            fill: '#ff0000',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 显示答案
        this.add.text(400, 290, '答案是：' + this.secretCode.map(c => c.display).join(' '), {
            fontSize: '20px',
            fill: '#ffffff'
        }).setOrigin(0.5);

        this.time.delayedCall(3000, () => {
            this.scene.start('MainMenu');
        });
    }
}
