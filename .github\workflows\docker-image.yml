name: Build and Push Docker Image

on:
  push:
    branches:
      - main # 当推送到 main 分支时触发
  workflow_dispatch: # 手动运行支持

env:
  IMAGE_NAME: darkbfly/phaser-games # 替换为你自己的镜像名
  TAG: ${{ github.sha }} # 使用 Git commit SHA 作为 tag，也可以改成 latest 等

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Log in to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_HUB_USERNAME }}
          password: ${{ secrets.DOCKER_HUB_TOKEN }}

      - name: Build Docker image
        run: |
          docker build . --tag $IMAGE_NAME:$TAG --tag $IMAGE_NAME:latest

      - name: Push Docker image
        run: |
          docker push $IMAGE_NAME:$TAG
          docker push $IMAGE_NAME:latest
