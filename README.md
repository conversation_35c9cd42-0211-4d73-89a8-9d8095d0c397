# 多游戏合集 - Phaser.js

一个使用Phaser.js框架开发的包含多种经典游戏的网页应用。

## 🎮 包含的游戏

### 1. 贪吃蛇 (Snake)
- **控制方式**: 方向键或WASD键
- **游戏目标**: 控制蛇吃食物，避免撞到墙壁或自己的身体
- **特色**: 随着分数增加，蛇的移动速度会逐渐加快

### 2. 打砖块 (Breakout)
- **控制方式**: 鼠标移动或方向键控制挡板
- **游戏目标**: 用球击破屏幕上的所有砖块
- **特色**: 不同颜色的砖块有不同的分数，球速会随着游戏进行而增加

### 3. 太空射击 (Space Shooter)
- **控制方式**: 方向键或WASD移动，空格键射击
- **游戏目标**: 控制飞船射击敌人，避免被击中
- **特色**: 多种敌人移动模式，随着分数增加难度递增

## 🚀 快速开始

### 方法一：直接打开
1. 下载所有文件到本地目录
2. 双击打开 `index.html` 文件

### 方法二：本地服务器（推荐）
1. 在项目目录下运行以下命令之一：
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   
   # Node.js (需要安装 http-server)
   npx http-server -p 8000
   ```
2. 在浏览器中访问 `http://localhost:8000`

## 🎯 游戏功能

### 通用功能
- **计分系统**: 实时显示当前分数
- **暂停功能**: 按P键或点击暂停按钮
- **返回菜单**: 按ESC键或点击返回按钮
- **响应式设计**: 支持不同屏幕尺寸

### 快捷键
- `ESC` - 返回主菜单
- `空格键` 或 `P` - 暂停/继续游戏
- `方向键` 或 `WASD` - 游戏控制

## 📁 项目结构

```
games/
├── index.html              # 主页面
├── styles/
│   └── main.css            # 样式文件
├── js/
│   ├── main.js             # 主入口文件
│   ├── gameManager.js      # 游戏管理器
│   └── games/
│       ├── snake.js        # 贪吃蛇游戏
│       ├── breakout.js     # 打砖块游戏
│       └── shooter.js      # 太空射击游戏
└── README.md               # 项目说明
```

## 🛠️ 技术栈

- **Phaser.js 3.70.0** - 游戏开发框架
- **HTML5 Canvas** - 游戏渲染
- **ES6+ JavaScript** - 游戏逻辑
- **CSS3** - 界面样式

## 🎨 特色功能

- **现代化UI设计**: 使用渐变背景和毛玻璃效果
- **流畅的动画效果**: 按钮悬停、游戏过渡动画
- **粒子效果**: 爆炸、碰撞等视觉效果
- **音效反馈**: 视觉反馈增强游戏体验
- **自适应难度**: 游戏难度随分数动态调整

## 🔧 自定义和扩展

### 添加新游戏
1. 在 `js/games/` 目录下创建新的游戏文件
2. 继承 `Phaser.Scene` 类
3. 在 `gameManager.js` 中添加游戏类型
4. 在 `index.html` 中添加游戏按钮和脚本引用

### 修改游戏参数
每个游戏文件中都有可调整的参数：
- 移动速度
- 难度递增
- 视觉效果
- 计分规则

## 📱 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

## 📄 许可证

本项目仅供学习和演示使用。

## 🤝 贡献

欢迎提交问题和改进建议！

---

**享受游戏时光！** 🎮✨
